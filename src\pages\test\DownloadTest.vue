<template>
  <div class="download-test">
    <van-nav-bar title="下载测试" left-arrow @click-left="$router.back()" />
    
    <div class="test-content">
      <van-cell-group>
        <van-cell title="设备信息" :value="deviceInfo" />
        <van-cell title="浏览器" :value="browserInfo" />
      </van-cell-group>
      
      <div class="test-buttons">
        <van-button 
          type="primary" 
          block 
          @click="testPdfDownload"
          :loading="loading.pdf"
        >
          测试PDF下载
        </van-button>
        
        <van-button 
          type="success" 
          block 
          @click="testExcelDownload"
          :loading="loading.excel"
        >
          测试Excel下载
        </van-button>
        
        <van-button 
          type="warning" 
          block 
          @click="testImageDownload"
          :loading="loading.image"
        >
          测试图片下载
        </van-button>
        
        <van-button 
          type="default" 
          block 
          @click="testWindowOpen"
        >
          测试原生window.open
        </van-button>
      </div>
      
      <div class="test-results">
        <van-cell-group title="测试结果">
          <van-cell 
            v-for="(result, index) in testResults" 
            :key="index"
            :title="result.method"
            :value="result.status"
            :label="result.time"
          />
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showNotify } from 'vant'
import { downloadFileForMobile, detectDevice } from '@/utils/mobileDownload'

const router = useRouter()

// 响应式数据
const loading = ref({
  pdf: false,
  excel: false,
  image: false
})

const testResults = ref([])
const device = ref({})

// 计算属性
const deviceInfo = computed(() => {
  if (!device.value) return '检测中...'
  
  const info = []
  if (device.value.isIOS) info.push('iOS')
  if (device.value.isAndroid) info.push('Android')
  if (device.value.isMobile) info.push('Mobile')
  if (device.value.isTablet) info.push('Tablet')
  
  return info.join(', ') || '桌面端'
})

const browserInfo = computed(() => {
  if (!device.value) return '检测中...'
  
  const info = []
  if (device.value.isSafari) info.push('Safari')
  if (device.value.isChrome) info.push('Chrome')
  if (device.value.isFirefox) info.push('Firefox')
  
  return info.join(', ') || '其他浏览器'
})

// 方法
const addTestResult = (method, status, error = null) => {
  testResults.value.unshift({
    method,
    status,
    time: new Date().toLocaleTimeString(),
    error
  })
  
  // 只保留最近10条记录
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

const testPdfDownload = async () => {
  loading.value.pdf = true
  
  try {
    // 使用一个测试PDF文件URL
    const testUrl = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
    
    const success = downloadFileForMobile(testUrl, 'test.pdf', (error) => {
      addTestResult('PDF下载', '被阻止', error.message)
      showNotify({ type: 'warning', message: '下载被浏览器阻止' })
    })
    
    if (success) {
      addTestResult('PDF下载', '成功')
      showNotify({ type: 'success', message: 'PDF下载已触发' })
    } else {
      addTestResult('PDF下载', '失败')
      showNotify({ type: 'danger', message: 'PDF下载失败' })
    }
    
  } catch (error) {
    addTestResult('PDF下载', '异常', error.message)
    showNotify({ type: 'danger', message: '下载异常: ' + error.message })
  } finally {
    loading.value.pdf = false
  }
}

const testExcelDownload = async () => {
  loading.value.excel = true
  
  try {
    // 创建一个简单的Excel文件数据URL
    const csvData = 'Name,Age,City\nJohn,30,New York\nJane,25,Los Angeles'
    const blob = new Blob([csvData], { type: 'text/csv' })
    const testUrl = URL.createObjectURL(blob)
    
    const success = downloadFileForMobile(testUrl, 'test.csv', (error) => {
      addTestResult('Excel下载', '被阻止', error.message)
      showNotify({ type: 'warning', message: '下载被浏览器阻止' })
    })
    
    if (success) {
      addTestResult('Excel下载', '成功')
      showNotify({ type: 'success', message: 'Excel下载已触发' })
    } else {
      addTestResult('Excel下载', '失败')
      showNotify({ type: 'danger', message: 'Excel下载失败' })
    }
    
    // 清理URL对象
    setTimeout(() => URL.revokeObjectURL(testUrl), 1000)
    
  } catch (error) {
    addTestResult('Excel下载', '异常', error.message)
    showNotify({ type: 'danger', message: '下载异常: ' + error.message })
  } finally {
    loading.value.excel = false
  }
}

const testImageDownload = async () => {
  loading.value.image = true
  
  try {
    // 使用一个测试图片URL
    const testUrl = 'https://via.placeholder.com/300x200/ff0000/ffffff?text=Test+Image'
    
    const success = downloadFileForMobile(testUrl, 'test.png', (error) => {
      addTestResult('图片下载', '被阻止', error.message)
      showNotify({ type: 'warning', message: '下载被浏览器阻止' })
    })
    
    if (success) {
      addTestResult('图片下载', '成功')
      showNotify({ type: 'success', message: '图片下载已触发' })
    } else {
      addTestResult('图片下载', '失败')
      showNotify({ type: 'danger', message: '图片下载失败' })
    }
    
  } catch (error) {
    addTestResult('图片下载', '异常', error.message)
    showNotify({ type: 'danger', message: '下载异常: ' + error.message })
  } finally {
    loading.value.image = false
  }
}

const testWindowOpen = () => {
  try {
    const testUrl = 'https://www.example.com'
    const newWindow = window.open(testUrl, '_blank', 'noopener,noreferrer')
    
    if (newWindow) {
      addTestResult('window.open', '成功')
      showNotify({ type: 'success', message: 'window.open 成功' })
    } else {
      addTestResult('window.open', '被阻止')
      showNotify({ type: 'warning', message: 'window.open 被阻止' })
    }
  } catch (error) {
    addTestResult('window.open', '异常', error.message)
    showNotify({ type: 'danger', message: 'window.open 异常: ' + error.message })
  }
}

// 生命周期
onMounted(() => {
  device.value = detectDevice()
})
</script>

<style lang="scss" scoped>
.download-test {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.test-content {
  padding: 16px;
}

.test-buttons {
  margin: 20px 0;
  
  .van-button {
    margin-bottom: 12px;
  }
}

.test-results {
  margin-top: 20px;
}
</style>
