/**
 * 移动端文件下载工具
 * 解决手机端 window.open 下载文件的兼容性问题
 */

/**
 * 检测是否为iOS Safari
 */
const isIOSSafari = () => {
  const userAgent = navigator.userAgent
  return /iPad|iPhone|iPod/.test(userAgent) && /^((?!chrome|android).)*safari/i.test(userAgent)
}

/**
 * 移动端文件下载函数（强制下载，不预览）
 * @param {string} url - 文件下载URL
 * @param {string} filename - 可选的文件名
 * @param {Function} onError - 错误回调函数
 * @returns {boolean} - 是否成功触发下载
 */
export const downloadFileForMobile = (url, filename = '', onError = null) => {
  try {
    // 方法1: 使用 a 标签强制下载
    const link = document.createElement('a')
    link.href = url
    link.download = filename || 'download'
    link.target = '_blank'
    link.rel = 'noopener noreferrer'
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // iOS Safari 备选方案
    if (isIOSSafari()) {
      setTimeout(() => {
        const newWindow = window.open(url, '_blank')
        if (!newWindow && onError) {
          onError(new Error('Download blocked'))
        }
      }, 100)
    }

    return true

  } catch (error) {
    console.warn('Download failed:', error)

    // 降级方案：直接使用 window.open
    try {
      window.open(url, '_blank')
      return true
    } catch (fallbackError) {
      if (onError) onError(fallbackError)
      return false
    }
  }
}





// 简单导出
export default downloadFileForMobile
