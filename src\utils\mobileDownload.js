/**
 * 移动端文件下载工具
 * 解决手机端 window.open 下载文件的兼容性问题
 */

/**
 * 检测设备和浏览器类型
 */
export const detectDevice = () => {
  const userAgent = navigator.userAgent
  
  return {
    isIOS: /iPad|iPhone|iPod/.test(userAgent),
    isAndroid: /Android/.test(userAgent),
    isSafari: /^((?!chrome|android).)*safari/i.test(userAgent),
    isChrome: /Chrome/.test(userAgent) && !/Edge/.test(userAgent),
    isFirefox: /Firefox/.test(userAgent),
    isMobile: /Mobi|Android/i.test(userAgent),
    isTablet: /iPad|Android(?!.*Mobile)/i.test(userAgent)
  }
}

/**
 * 移动端优化的文件下载函数
 * @param {string} url - 文件下载URL
 * @param {string} filename - 可选的文件名
 * @param {Function} onError - 错误回调函数
 * @returns {boolean} - 是否成功触发下载
 */
export const downloadFileForMobile = (url, filename = '', onError = null) => {
  const device = detectDevice()
  
  try {
    // 方法1: 使用 a 标签下载（推荐方法，兼容性最好）
    const link = document.createElement('a')
    link.href = url
    
    // 设置下载属性
    if (filename) {
      link.download = filename
    } else {
      link.download = '' // 让浏览器决定文件名
    }
    
    link.target = '_blank'
    link.rel = 'noopener noreferrer'
    
    // 设置样式确保不可见
    link.style.display = 'none'
    
    // 添加到DOM并触发点击
    document.body.appendChild(link)
    link.click()
    
    // 清理DOM
    setTimeout(() => {
      document.body.removeChild(link)
    }, 100)
    
    // iOS Safari 特殊处理
    if (device.isIOS && device.isSafari) {
      // 延迟执行 window.open 作为备选方案
      setTimeout(() => {
        try {
          const newWindow = window.open(url, '_blank', 'noopener,noreferrer')
          if (!newWindow || newWindow.closed) {
            // 如果弹窗被阻止，尝试最后的降级方案
            handleDownloadFallback(url, onError)
          }
        } catch (error) {
          handleDownloadFallback(url, onError)
        }
      }, 100)
    }
    
    return true
    
  } catch (error) {
    console.warn('Primary download method failed:', error)
    return handleDownloadFallback(url, onError)
  }
}

/**
 * 下载失败时的降级处理
 * @param {string} url - 文件URL
 * @param {Function} onError - 错误回调函数
 * @returns {boolean} - 是否成功触发下载
 */
const handleDownloadFallback = (url, onError) => {
  try {
    // 方法2: 尝试 window.open
    const newWindow = window.open(url, '_blank', 'noopener,noreferrer')
    
    if (!newWindow || newWindow.closed) {
      // 方法3: 直接跳转（最后的降级方案）
      window.location.href = url
      return true
    }
    
    return true
    
  } catch (error) {
    console.error('All download methods failed:', error)
    
    // 调用错误回调
    if (typeof onError === 'function') {
      onError(error)
    }
    
    return false
  }
}

/**
 * 创建下载链接元素（用于手动下载）
 * @param {string} url - 文件URL
 * @param {string} text - 链接文本
 * @param {string} filename - 文件名
 * @returns {HTMLElement} - 下载链接元素
 */
export const createDownloadLink = (url, text = '点击下载', filename = '') => {
  const link = document.createElement('a')
  link.href = url
  link.textContent = text
  link.target = '_blank'
  link.rel = 'noopener noreferrer'
  
  if (filename) {
    link.download = filename
  }
  
  // 添加样式
  link.style.cssText = `
    color: #1989fa;
    text-decoration: underline;
    cursor: pointer;
    padding: 8px 12px;
    border: 1px solid #1989fa;
    border-radius: 4px;
    background: white;
    display: inline-block;
    margin: 4px;
  `
  
  return link
}

/**
 * 检查URL是否可访问
 * @param {string} url - 要检查的URL
 * @returns {Promise<boolean>} - URL是否可访问
 */
export const checkUrlAccessible = async (url) => {
  try {
    const response = await fetch(url, { 
      method: 'HEAD',
      mode: 'no-cors' // 避免CORS问题
    })
    return true
  } catch (error) {
    console.warn('URL accessibility check failed:', error)
    return false
  }
}

/**
 * 获取文件扩展名
 * @param {string} url - 文件URL
 * @returns {string} - 文件扩展名
 */
export const getFileExtension = (url) => {
  try {
    const pathname = new URL(url).pathname
    const extension = pathname.split('.').pop().toLowerCase()
    return extension || ''
  } catch (error) {
    // 如果URL解析失败，尝试从字符串中提取
    const match = url.match(/\.([a-zA-Z0-9]+)(?:\?|$)/)
    return match ? match[1].toLowerCase() : ''
  }
}

/**
 * 根据文件类型判断是否应该下载还是预览
 * @param {string} url - 文件URL
 * @returns {object} - 包含建议操作的对象
 */
export const getFileAction = (url) => {
  const extension = getFileExtension(url)
  const device = detectDevice()
  
  // 定义不同文件类型的处理策略
  const fileTypes = {
    // 通常需要下载的文件
    download: ['xlsx', 'xls', 'doc', 'docx', 'zip', 'rar', '7z', 'exe', 'msi'],
    // 可以预览的文件
    preview: ['pdf', 'jpg', 'jpeg', 'png', 'gif', 'svg', 'txt'],
    // 视频文件
    video: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
    // 音频文件
    audio: ['mp3', 'wav', 'ogg', 'aac', 'm4a']
  }
  
  let action = 'download' // 默认下载
  let method = 'link' // 默认使用链接方式
  
  if (fileTypes.preview.includes(extension)) {
    action = 'preview'
    method = device.isMobile ? 'window' : 'window'
  } else if (fileTypes.video.includes(extension) || fileTypes.audio.includes(extension)) {
    action = 'preview'
    method = 'window'
  } else if (fileTypes.download.includes(extension)) {
    action = 'download'
    method = 'link'
  }
  
  return {
    action, // 'download' | 'preview'
    method, // 'link' | 'window'
    extension,
    shouldDownload: action === 'download'
  }
}

export default {
  downloadFileForMobile,
  createDownloadLink,
  checkUrlAccessible,
  getFileExtension,
  getFileAction,
  detectDevice
}
