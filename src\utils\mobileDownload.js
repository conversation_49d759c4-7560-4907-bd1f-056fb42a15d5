/**
 * 移动端文件下载工具
 * 解决手机端 window.open 下载文件的兼容性问题
 */

/**
 * 检测设备和浏览器类型
 */
export const detectDevice = () => {
  const userAgent = navigator.userAgent
  
  return {
    isIOS: /iPad|iPhone|iPod/.test(userAgent),
    isAndroid: /Android/.test(userAgent),
    isSafari: /^((?!chrome|android).)*safari/i.test(userAgent),
    isChrome: /Chrome/.test(userAgent) && !/Edge/.test(userAgent),
    isFirefox: /Firefox/.test(userAgent),
    isMobile: /Mobi|Android/i.test(userAgent),
    isTablet: /iPad|Android(?!.*Mobile)/i.test(userAgent)
  }
}

/**
 * 移动端优化的文件下载函数（强制下载，不预览）
 * @param {string} url - 文件下载URL
 * @param {string} filename - 可选的文件名
 * @param {Function} onError - 错误回调函数
 * @returns {boolean} - 是否成功触发下载
 */
export const downloadFileForMobile = (url, filename = '', onError = null) => {
  const device = detectDevice()

  try {
    // 方法1: 使用 a 标签强制下载（推荐方法）
    const link = document.createElement('a')
    link.href = url

    // 强制设置下载属性，确保下载而不是预览
    if (filename) {
      link.download = filename
    } else {
      // 从URL中提取文件名，如果没有则使用默认名称
      const urlPath = new URL(url, window.location.origin).pathname
      const extractedFilename = urlPath.split('/').pop() || 'download'
      link.download = extractedFilename
    }

    link.target = '_blank'
    link.rel = 'noopener noreferrer'

    // 设置样式确保不可见
    link.style.display = 'none'

    // 添加到DOM并触发点击
    document.body.appendChild(link)
    link.click()

    // 清理DOM
    setTimeout(() => {
      if (document.body.contains(link)) {
        document.body.removeChild(link)
      }
    }, 100)

    // iOS Safari 特殊处理 - 对于某些文件类型可能需要额外处理
    if (device.isIOS && device.isSafari) {
      // 延迟执行备选方案
      setTimeout(() => {
        try {
          // 对于iOS Safari，某些情况下需要使用window.open
          // 但添加download参数提示浏览器下载
          const downloadUrl = addDownloadHint(url, filename)
          const newWindow = window.open(downloadUrl, '_blank', 'noopener,noreferrer')

          if (!newWindow || newWindow.closed) {
            // 如果弹窗被阻止，尝试最后的降级方案
            handleDownloadFallback(url, onError)
          }
        } catch (error) {
          handleDownloadFallback(url, onError)
        }
      }, 100)
    }

    return true

  } catch (error) {
    console.warn('Primary download method failed:', error)
    return handleDownloadFallback(url, onError)
  }
}

/**
 * 为URL添加下载提示参数
 * @param {string} url - 原始URL
 * @param {string} filename - 文件名
 * @returns {string} - 添加了下载提示的URL
 */
const addDownloadHint = (url, filename) => {
  try {
    const urlObj = new URL(url, window.location.origin)

    // 添加下载相关的查询参数
    if (filename) {
      urlObj.searchParams.set('download', filename)
    } else {
      urlObj.searchParams.set('download', '1')
    }

    // 添加Content-Disposition提示
    urlObj.searchParams.set('attachment', '1')

    return urlObj.toString()
  } catch (error) {
    // 如果URL解析失败，返回原始URL
    return url
  }
}

/**
 * 下载失败时的降级处理
 * @param {string} url - 文件URL
 * @param {Function} onError - 错误回调函数
 * @returns {boolean} - 是否成功触发下载
 */
const handleDownloadFallback = (url, onError) => {
  try {
    // 方法2: 尝试 window.open 强制下载
    const downloadUrl = addDownloadHint(url)
    const newWindow = window.open(downloadUrl, '_blank', 'noopener,noreferrer')

    if (!newWindow || newWindow.closed) {
      // 方法3: 直接跳转（最后的降级方案）
      window.location.href = downloadUrl
      return true
    }

    return true

  } catch (error) {
    console.error('All download methods failed:', error)

    // 调用错误回调
    if (typeof onError === 'function') {
      onError(error)
    }

    return false
  }
}

/**
 * 创建下载链接元素（用于手动下载）
 * @param {string} url - 文件URL
 * @param {string} text - 链接文本
 * @param {string} filename - 文件名
 * @returns {HTMLElement} - 下载链接元素
 */
export const createDownloadLink = (url, text = '点击下载', filename = '') => {
  const link = document.createElement('a')
  link.href = url
  link.textContent = text
  link.target = '_blank'
  link.rel = 'noopener noreferrer'
  
  if (filename) {
    link.download = filename
  }
  
  // 添加样式
  link.style.cssText = `
    color: #1989fa;
    text-decoration: underline;
    cursor: pointer;
    padding: 8px 12px;
    border: 1px solid #1989fa;
    border-radius: 4px;
    background: white;
    display: inline-block;
    margin: 4px;
  `
  
  return link
}

/**
 * 检查URL是否可访问
 * @param {string} url - 要检查的URL
 * @returns {Promise<boolean>} - URL是否可访问
 */
export const checkUrlAccessible = async (url) => {
  try {
    await fetch(url, {
      method: 'HEAD',
      mode: 'no-cors' // 避免CORS问题
    })
    return true
  } catch (error) {
    console.warn('URL accessibility check failed:', error)
    return false
  }
}

/**
 * 获取文件扩展名
 * @param {string} url - 文件URL
 * @returns {string} - 文件扩展名
 */
export const getFileExtension = (url) => {
  try {
    const pathname = new URL(url).pathname
    const extension = pathname.split('.').pop().toLowerCase()
    return extension || ''
  } catch (error) {
    // 如果URL解析失败，尝试从字符串中提取
    const match = url.match(/\.([a-zA-Z0-9]+)(?:\?|$)/)
    return match ? match[1].toLowerCase() : ''
  }
}

/**
 * 根据文件类型判断下载方式（统一为下载，不预览）
 * @param {string} url - 文件URL
 * @returns {object} - 包含建议操作的对象
 */
export const getFileAction = (url) => {
  const extension = getFileExtension(url)

  // 定义不同文件类型的处理策略 - 全部设为下载
  const fileTypes = {
    // 办公文档
    office: ['xlsx', 'xls', 'doc', 'docx', 'ppt', 'pptx'],
    // PDF文档
    pdf: ['pdf'],
    // 图片文件
    image: ['jpg', 'jpeg', 'png', 'gif', 'svg', 'bmp', 'webp'],
    // 压缩文件
    archive: ['zip', 'rar', '7z', 'tar', 'gz'],
    // 可执行文件
    executable: ['exe', 'msi', 'dmg', 'pkg'],
    // 视频文件
    video: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'],
    // 音频文件
    audio: ['mp3', 'wav', 'ogg', 'aac', 'm4a', 'flac'],
    // 文本文件
    text: ['txt', 'csv', 'json', 'xml', 'log']
  }

  // 统一返回下载操作
  let method = 'link' // 默认使用链接方式下载

  // 对于某些特殊文件类型，可能需要使用window方式
  if (fileTypes.executable.includes(extension)) {
    method = 'window' // 可执行文件使用window.open方式
  }

  return {
    action: 'download', // 统一为下载
    method, // 'link' | 'window'
    extension,
    shouldDownload: true, // 始终为true
    fileType: getFileType(extension, fileTypes)
  }
}

/**
 * 获取文件类型分类
 * @param {string} extension - 文件扩展名
 * @param {object} fileTypes - 文件类型定义
 * @returns {string} - 文件类型分类
 */
const getFileType = (extension, fileTypes) => {
  for (const [type, extensions] of Object.entries(fileTypes)) {
    if (extensions.includes(extension)) {
      return type
    }
  }
  return 'unknown'
}

/**
 * 强制下载文件（确保下载而不是预览）
 * @param {string} url - 文件URL
 * @param {string} filename - 文件名
 * @param {Function} onError - 错误回调
 * @returns {boolean} - 是否成功触发下载
 */
export const forceDownloadFile = (url, filename = '', onError = null) => {
  const device = detectDevice()

  try {
    // 创建隐藏的iframe来处理下载
    const iframe = document.createElement('iframe')
    iframe.style.display = 'none'
    iframe.style.position = 'absolute'
    iframe.style.left = '-9999px'
    iframe.style.top = '-9999px'

    // 设置iframe的src为下载URL
    const downloadUrl = addDownloadHint(url, filename)
    iframe.src = downloadUrl

    // 添加到DOM
    document.body.appendChild(iframe)

    // 清理iframe
    setTimeout(() => {
      if (document.body.contains(iframe)) {
        document.body.removeChild(iframe)
      }
    }, 5000)

    // 同时使用a标签作为备选方案
    setTimeout(() => {
      downloadFileForMobile(url, filename, onError)
    }, 100)

    return true

  } catch (error) {
    console.warn('Force download failed, falling back to normal download:', error)
    return downloadFileForMobile(url, filename, onError)
  }
}

/**
 * 创建Blob下载（用于确保下载行为）
 * @param {string} url - 文件URL
 * @param {string} filename - 文件名
 * @param {Function} onError - 错误回调
 * @returns {Promise<boolean>} - 是否成功触发下载
 */
export const downloadFileAsBlob = async (url, filename = '', onError = null) => {
  try {
    // 获取文件数据
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()

    // 创建Blob URL
    const blobUrl = URL.createObjectURL(blob)

    // 使用a标签下载
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = filename || 'download'
    link.style.display = 'none'

    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理Blob URL
    setTimeout(() => URL.revokeObjectURL(blobUrl), 1000)

    return true

  } catch (error) {
    console.warn('Blob download failed:', error)
    if (typeof onError === 'function') {
      onError(error)
    }
    // 降级到普通下载
    return downloadFileForMobile(url, filename, onError)
  }
}

export default {
  downloadFileForMobile,
  forceDownloadFile,
  downloadFileAsBlob,
  createDownloadLink,
  checkUrlAccessible,
  getFileExtension,
  getFileAction,
  detectDevice
}
