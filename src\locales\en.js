export default {
  common: {
    appName: 'ERPs Parts',
    slogan: 'Parts Management System',
    home: 'Home',
    catalog: 'Catalog',
    order: 'Orders',
    notice: 'Notices',
    cart: 'Cart',
    user: 'Profile',
    logout: 'Logout',
    copyright: '© 2025. TURF ONE All Rights Reserved.',
    submit: 'Submit',
    cancel: 'Cancel',
    error: 'Operation failed, please try again later',
    warranty: 'Warranty',
    more: 'More',
    all: 'All',
    confirmText: 'Are you sure to confirm this operation?',
    cancelConfirm: 'Are you sure to cancel this operation?',
    noMoreData: 'No more data',
    reset: 'Reset',
    close: 'Close',
    confirm: 'Confirm',
    loading: 'Loading...',
    formValidationFailed: 'Form validation failed, please check your input',
    retry: 'Retry',
    languageChanged: 'Language changed to {language}',
    search: 'Search',
    operationSuccess: 'Operation successful',
    operationWarning: 'Operation warning',
    operationError: 'Operation failed'
  },
  home: {
    quickAccess: 'Quick Access',
    equipmentSearch: 'Equipment Search',
    partsSearch: 'Parts Search',
    equipmentSearchPlaceholder: 'Equipment Model, Description, Model Number, Service Code, Serial Number',
    partsSearchPlaceholder: 'Part Number, Part Name, Description',
    filterConditions: 'Filter Conditions',
    brand: 'Brand',
    equipmentType: 'Equipment Type',
    equipmentModel: 'Equipment Model',
    serviceCode: 'Service Code',
    selectBrand: 'Please Select Brand',
    selectEquipmentType: 'Please Select Equipment Type',
    selectEquipmentModel: 'Please Select Equipment Model',
    selectServiceCode: 'Please Select Service Code',
    reset: 'Reset',
    applyFilter: 'Apply Filter',
    quantity: 'Quantity',
    price: 'Price',
    addToCart: 'Add to Cart',
    removeFromCart: 'Remove',
    addToFavorites: 'Add to Favorites',
    removeFromFavorites: 'Remove from Favorites',
    viewDetails: 'View Details',
    viewBom: 'View BOM',
    viewBomWithLocation: 'View BOM and Locate Part',
    loadMore: 'Load More',
    loading: 'Loading...',
    loadError: 'Load failed, tap to retry',
    noMoreData: 'No more data',
    noEquipmentData: 'No Equipment Data',
    noPartsData: 'No Parts Data',
    searchFailed: 'Search failed, please try again',
    operationFailed: 'Operation failed, please try again',
    addSuccess: 'Added successfully',
    removeSuccess: 'Removed successfully',
    favoriteSuccess: 'Added to favorites successfully',
    unfavoriteSuccess: 'Removed from favorites successfully',
    model: 'Model',
    description: 'Description',
    component: 'Component',
    partNumber: 'Part Number',
    stock: 'Stock',
    specification: 'Specification',
    relatedImages: 'Related Images',
    allImages: 'All Images',
    viewMoreImages: 'View More Images',
    collapseImages: 'Collapse Images',
    expand: 'Expand',
    collapse: 'Collapse',
    noDetailAvailable: 'No detail information available',
    viewAll: 'View All'
  },
  detail: {
    productDetail: 'Product Detail',
    bomChart: 'BOM Chart',
    menu: 'Menu',
    loading: 'Loading...',
    selectFromMenu: 'Please select content from the menu to view',
    partsList: 'Parts List',
    export: 'Export',
    exportPdf: 'Export PDF',
    exportXlsx: 'Export Excel',
    invalidFile: 'Invalid file',
    loadMenuFailed: 'Failed to load menu',
    loadProductFailed: 'Failed to load product information',
    addToCartSuccess: 'Added to cart successfully',
    removeFromCartSuccess: 'Removed from cart successfully',
    cartOperationFailed: 'Cart operation failed',
    addToFavoritesSuccess: 'Added to favorites successfully',
    removeFromFavoritesSuccess: 'Removed from favorites successfully',
    favoritesOperationFailed: 'Favorites operation failed',
    searchMenu: 'Search menu...',
    noSearchResults: 'No results found',
    totalItems: 'Total {count} items',
    folders: '{count} folders',
    files: '{count} files',
    loadingMenu: 'Loading menu...',
    itemCount: '{count} items',
    emptyMenu: 'No menu content',
    back: 'Back',
    manual: 'Manual',
    diagram: 'Diagram',
    video: 'Video',
    product: 'Product',
    file: 'File',
    title: 'Product Detail',
    productInfo: 'Product Info',
    noBomData: 'No BOM data available',
    noProductData: 'No product data available',
    noPartsData: 'No parts data available',
    downloadExcel: 'Download Excel',
    viewPDF: 'View PDF',
    viewCart: 'View Cart',
    refresh: 'Refresh',
    share: 'Share',
    feedback: 'Feedback',
    invalidId: 'Invalid product ID',
    loadFailed: 'Load failed',
    selectQuantity: 'Select Quantity',
    partNumber: 'Part Number',
    description: 'Description',
    price: 'Price',
    quantity: 'Quantity',
    stock: 'Stock',
    parameters: 'Parameters',
    specifications: 'Specifications',
    recommendedQuantity: 'Recommended Quantity',
    remarks: 'Remarks',
    partNumberCopied: 'Part number copied',
    copyFailed: 'Copy failed',
    addToCart: 'Add to Cart',
    removeFromCart: 'Remove from Cart',
    addToFavorites: 'Add to Favorites',
    removeFromFavorites: 'Remove from Favorites',
    searchParts: 'Search Parts',
    inCartOnly: 'Cart Only',
    favoritesOnly: 'Favorites Only',
    index: 'Index',
    add: 'Add',
    remove: 'Remove',
    techInfo: 'Tech Info',
    selectedCount: '{count} items selected',
    batchAddToCart: 'Batch Add to Cart',
    batchAddToFavorites: 'Batch Add to Favorites',
    clearSelection: 'Clear Selection',
    noCartItems: 'No items in cart',
    noFavoriteItems: 'No favorite items',
    fittedOn: 'Fitted On',
    noticeOfChange: 'Notice of Change',
    createDate: 'Create Date',
    serialNumberRange: 'Serial Number Range',
    attachments: 'Attachments',
    notSpecified: 'Not Specified',
    noDescription: 'No Description',
    noFittedOnData: 'No fitted equipment data',
    fittedOnNote: 'Equipment information for this part is not maintained',
    noNoticeData: 'No change notices',
    noticeNote: 'No change notices for this part',
    viewDetail: 'View Detail',
    materialType: 'Material Type',
    weight: 'Weight',
    dimensions: 'Dimensions',
    manufacturer: 'Manufacturer',
    warranty: 'Warranty',
    noRemarks: 'No Remarks',
    equipmentDetailTip: 'Equipment detail feature under development',
    downloadStarted: 'Download started',
    downloadFailed: 'Download failed',
    openPdfFailed: 'Failed to open PDF',
    noPdfAvailable: 'No PDF document available',
    addedToCart: 'Added to cart',
    addedToFavorites: 'Added to favorites',
    removedFromCart: 'Removed from cart',
    removedFromFavorites: 'Removed from favorites',
    operationFailed: 'Operation failed',
    shareSuccess: 'Share successful',
    shareTitle: 'Product Share',
    copiedToClipboard: 'Copied to clipboard',
    feedbackTip: 'Feedback feature under development',
    noBomIdForExcel: 'Cannot download Excel, missing BOM ID',
    equipmentDetailNotImplemented: 'Equipment detail feature not implemented yet',
    openPdf: 'Open PDF',
    refreshData: 'Refresh Data',
    refreshSuccess: 'Refresh successful',
    refreshFailed: 'Refresh failed',
    fittedOnEquipment: 'Fitted Equipment',
    model: 'Model',
    serviceCode: 'Service Code'
  },
  product: {
    noImage: 'No image available',
    unknownProduct: 'Unknown product',
    unknown: 'Unknown',
    partNumber: 'Part Number',
    description: 'Description',
    specifications: 'Specifications',
    quantity: 'Quantity',
    weight: 'Weight',
    dimensions: 'Dimensions',
    addToCart: 'Add to Cart',
    removeFromCart: 'Remove from Cart',
    updateCart: 'Update Cart',
    addToFavorites: 'Add to Favorites',
    removeFromFavorites: 'Remove from Favorites',
    selectQuantity: 'Select Quantity',
    inCart: 'In Cart',
    inFavorites: 'In Favorites',
    stockStatus: 'Stock Status',
    available: 'Available',
    inStock: 'In Stock',
    lowStock: 'Low Stock',
    outOfStock: 'Out of Stock',
    productName: 'Product Name',
    productNameEn: 'English Name',
    brand: 'Brand',
    model: 'Model',
    category: 'Category',
    warranty: 'Warranty',
    parameterImage: 'Parameter Image',
    loadingProduct: 'Loading product information...',
    loadingImage: 'Loading image...',
    imageLoadFailed: 'Image load failed',
    info: 'Product Info'
  },
  file: {
    pdfHandbook: 'PDF Handbook',
    previewOnline: 'Preview Online',
    download: 'Download',
    pdfDocument: 'PDF Document',
    pdfDescription: 'You can preview online or download this document',
    videoFile: 'Video File',
    loadingVideo: 'Loading video...',
    videoNotSupported: 'Your browser does not support video playback',
    videoLoadFailed: 'Video loading failed',
    unknownFile: 'Unknown file',
    fileType: 'File Type',
    invalidFilePath: 'Invalid file path',
    pdfLoadSuccess: 'PDF loaded successfully',
    pdfLoadFailed: 'PDF loading failed',
    pdfRenderFailed: 'PDF rendering failed',
    downloadFailed: 'Download failed',
    jumpToPage: 'Jump to page',
    jump: 'Jump',
    invalidPageNumber: 'Invalid page number',
    duration: 'Duration',
    fileSize: 'File Size',
    lastModified: 'Last Modified',
    preview: 'Preview',
    share: 'Share',
    retry: 'Retry',
    previewNotSupported: 'Preview not supported for this file type',
    linkCopied: 'Link copied to clipboard',
    copyFailed: 'Copy failed',
    shareNotSupported: 'Share not supported in current browser',
    filePreview: 'File Preview'
  },
  language: {
    zh: 'Chinese',
    en: 'English'
  },
  login: {
    title: 'User Login',
    username: 'Username',
    password: 'Password',
    usernameRequired: 'Please enter username',
    passwordRequired: 'Please enter password',
    rememberMe: 'Remember Me',
    loginButton: 'Login',
    loginSuccess: 'Login Successful',
    loginFail: 'Login Failed',
    loginError: 'Login Error, Please try again later'
  },
  notice: {
    empty: 'No notifications',
    title: 'Notices',
    search: 'Search',
    partNumber: 'Part Number',
    status: {
      all: 'All',
      read: 'Read',
      unread: 'Unread'
    },
    filter: {
      title: 'Filter',
      status: 'Status',
      equipment: 'Equipment/Engine',
      equipmentType: 'Equipment Type',
      equipmentModel: 'Equipment Model',
      serviceCode: 'Service Code',
      clear: 'Clear Filter',
      clearTitle: 'Clear Filter Conditions',
      clearMessage: 'Are you sure to clear all equipment filter conditions?',
      clearSuccess: 'Filter conditions cleared'
    },
    list: {
      creationTime: 'Creation Time',
      status: 'Status',
      pdf: 'PDF',
      description: 'Description',
      equipmentModel: 'Equipment Model',
      serviceCode: 'Service Code',
      partNumber: 'Part Number',
      serialNumberRange: 'Serial Number Range'
    },
    loadMore: 'Load More',
    refresh: 'Refresh',
    noData: 'No Data',
    loadError: 'Load failed, please retry',
    viewPdf: 'View PDF'
  },
  validation: {
    required: 'This field is required',
    passwordLength: 'Password must be at least 6 characters',
    passwordMatch: 'Passwords do not match'
  },
  bom: {
    model: 'Model',
    operationControl: 'Operation Control',
    zoomIn: 'Zoom In',
    zoomOut: 'Zoom Out',
    reset: 'Reset',
    viewOptions: 'View Options',
    showList: 'Show List',
    hideList: 'Hide List',
    partsList: 'Parts List',
    partNumber: 'Part Number',
    quantity: 'Quantity',
    price: 'Price',
    serialNumber: 'Serial No.',
    addToCart: 'Add to Cart',
    removeFromCart: 'Remove',
    addToFavorites: 'Favorite',
    imageLoadFailed: 'Image load failed',
    addSuccess: 'Added successfully',
    removeSuccess: 'Removed successfully',
    operationFailed: 'Operation failed',
    missingParams: 'Missing required parameters',
    loadFailed: 'Load failed',
    landscapeMode: 'landscape',
    portraitMode: 'portrait',
    adaptedToMode: 'Adapted to {mode} mode',
    partNotFound: 'Part not found',
    partLocationNotFound: 'Part location not found',
    partLocated: 'Located part: {name}',
    loadingImage: 'Loading image...',
    loadingData: 'Loading data...',
    initializingCanvas: 'Initializing canvas...'
  },

  user: {
    myOrders: 'My Orders',
    myFavorites: 'My Favorites',
    myWarranty: 'My Warranty',
    myAddress: 'My Address',
    myCart: 'My Cart',
    accountSecurity: 'Account Security',
    privacySettings: 'Privacy Settings',
    language: 'Language',
    about: 'About Us',
    feedback: 'Feedback',
    logoutTitle: 'Logout',
    logoutConfirm: 'Are you sure you want to logout?',
    logoutSuccess: 'Logout successful',
    guest: 'Guest',
    loginName: 'Account',
    profile: 'Profile',
    changePassword: 'Change Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    passwordUpdateSuccess: 'Password updated successfully',
    profileUpdateSuccess: 'Profile updated successfully',
    dealerName: 'Dealer Name',
    ein: 'Employer Identification Number',
    dba: 'Doing Business As',
    website: 'Website',
    street: 'Business Street Address',
    city: 'City',
    state: 'State',
    postalCode: 'Zip/Postal Code',
    contactName: 'Primary Contact Name',
    contactTitle: 'Primary Contact Title',
    phone: 'Primary Phone',
    email: 'Primary E-mail',
    laborShopRate: 'Posted Labor Shop Rate'
  },
  catalog: {
    title: 'Product Catalog',
    brandList: 'Brand List',
    productList: 'Product List',
    equipmentTab: 'Equipment',
    spareParts: 'Spare Parts',
    videoTab: 'Video',
    manualTab: 'Manual',
    loadingBrands: 'Loading brands...',
    loadingProducts: 'Loading products...',
    loadingFailed: 'Loading failed',
    noBrandsData: 'No brands data',
    noProductsData: 'No products data',
    selectBrand: 'Select Brand',
    backToBrands: 'Back to Brands',
    productDetails: 'Product Details',
    specifications: 'Specifications',
    bomDiagram: 'BOM Diagram',
    relatedParts: 'Related Parts',
    viewBom: 'View BOM',
    viewManual: 'View Manual',
    playVideo: 'Play Video',
    downloadManual: 'Download Manual',
    addToCart: 'Add to Cart',
    addToFavorites: 'Add to Favorites',
    removeFromCart: 'Remove',
    removeFromFavorites: 'Remove from Favorites',
    quantity: 'Quantity',
    price: 'Price',
    stock: 'Stock',
    partNumber: 'Part Number',
    description: 'Description',
    model: 'Model',
    component: 'Component',
    operationSuccess: 'Operation successful',
    operationFailed: 'Operation failed',
    addSuccess: 'Added successfully',
    removeSuccess: 'Removed successfully',
    favoriteSuccess: 'Added to favorites successfully',
    unfavoriteSuccess: 'Removed from favorites successfully',
    confirmQuantity: 'Confirm Quantity',
    enterQuantity: 'Please enter quantity',
    cancel: 'Cancel',
    confirm: 'Confirm',
    viewDetails: 'View Details',
    noMoreData: 'No more data',
    loadMore: 'Load More',
    refreshing: 'Refreshing...',
    pullToRefresh: 'Pull to refresh',
    releaseToRefresh: 'Release to refresh'
  },
  cart: {
    title: 'Shopping Cart',
    favorites: 'My Favorites',
    empty: 'Cart is empty',
    favoritesEmpty: 'No favorite items',
    loadingFailed: 'Failed to load cart',
    loadingFavoritesFailed: 'Failed to load favorites',
    selectAll: 'Select All',
    selected: 'Selected',
    selectedItems: 'Selected Items',
    items: 'items',
    total: 'Total:',
    delete: 'Delete',
    createOrder: 'Create Order',
    component: 'Component',
    none: 'None',
    quantity: 'Quantity',
    price: 'Unit Price',
    addToCart: 'Add to Cart',
    addToFavorites: 'Add to Favorites',
    removeFromCart: 'Remove from Cart',
    removeFromFavorites: 'Remove from Favorites',
    operationFailed: 'Operation Failed',
    pleaseSelectItems: 'Please select items',
    deleteConfirm: 'Are you sure you want to delete selected items?',
    deleteSuccess: 'Deleted successfully',
    updateQuantityFailed: 'Failed to update quantity',
    viewBomChart: 'View BOM Chart',
    usedIn: 'Used in',
    showMore: 'Show More',
    showLess: 'Show Less',
    imageCount: 'images',
    previewImages: 'Preview Images',
    landscapeMode: 'Landscape Mode',
    portraitMode: 'Portrait Mode',
    bomChart: 'BOM Chart',
    close: 'Close'
  },
  warranty: {
    title: 'Warranty',
    searchRegistration: 'Search Registration',
    registerNew: 'Register New Product',
    pendingStatus: 'To be confirmed',
    confirmedStatus: 'Confirmed',
    name: 'Name',
    phone: 'Phone',
    email: 'Email',
    startDate: 'Start Date',
    endDate: 'End Date',
    status: 'Status',
    selectDate: 'Select Date',
    details: 'Details',
    claimDetails: 'Claim Details',
    confirm: 'Confirm',
    invalid: 'Invalid',
    claims: 'Warranty Claims',
    addClaimRecord: 'Add Claim',
    customerInfo: 'Customer Information',
    equipmentInfo: 'Equipment Information',
    salesDate: 'Sales Date',
    serialNumber: 'Serial Number',
    productType: 'Product Type',
    brandName: 'Brand Name',
    model: 'Model',
    serviceCode: 'Service Code',
    failureDate: 'Failure Date',
    circumstances: 'Circumstances',
    description: 'Description',
    uploadPictures: 'Upload Pictures',
    note: 'Note',
    createDate: 'Creation Date',
    address: 'Address',
    contactInfo: 'Contact Information',
    operateSuccess: 'Operation Successful',
    operateFailed: 'Operation Failed',
    confirmOperation: 'Confirm Operation',
    confirmInvalid: 'Are you sure you want to mark this registration as invalid?',
    confirmInvalidClaim: 'Are you sure you want to mark this claim as invalid?',
    invalidClaimTitle: 'Invalidate Claim',
    invalidClaimMessage: 'Are you sure you want to invalidate this claim record?',
    noData: 'No Data',
    searchCustomer: 'Search by name, phone, email...',
    editCustomer: 'Edit Customer',
    newCustomer: 'New Customer',
    deleteCustomer: 'Delete',
    selectAndClose: 'Select and Close',
    country: 'Country',
    city: 'City',
    state: 'State',
    searchTitle: 'Search Criteria',
    keywords: 'Keywords',
    filter: 'Filter',
    activeFilters: 'Active Filters',
    foundResults: 'Found',
    results: 'results',
    selectCustomer: 'Select Customer',
    selectBrand: 'Select Brand',
    selectProductType: 'Select Product Type',
    selectModel: 'Select Model',
    selectServiceCode: 'Select Service Code',
    selectStartDate: 'Select Start Date',
    selectEndDate: 'Select End Date',
    selectSalesDate: 'Select Sales Date',
    selectFailureDate: 'Select Failure Date',
    allRegistrations: 'All Registrations',
    faultInfo: 'Fault Information',
    claimRecords: 'Claim Records',
    noChanges: 'No changes to save',
    dates: 'Date Information',
    pleaseUploadImages: 'Please upload images',
    pleaseSelectCustomer: 'Please select a customer',
    confirmTitle: 'Confirm Registration',
    confirmMessage: 'Are you sure you want to confirm this product registration?',
    invalidTitle: 'Invalidate Registration',
    invalidMessage: 'Are you sure you want to invalidate this product registration?',
    onlyImageAllowed: 'Only JPG, JPEG and PNG images are allowed',
    imageSizeLimit: 'Image size cannot exceed 5MB'
  }
}